import 'package:flutter_test/flutter_test.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

void main() {
  group('MainPageNavigation', () {
    test('should have correct tab indices', () {
      expect(MainPageNavigation.dashboardTab, 0);
      expect(MainPageNavigation.trackingTab, 1);
      expect(MainPageNavigation.matchingTab, 2);
      expect(MainPageNavigation.searchTab, 3);
      expect(MainPageNavigation.profileTab, 4);
    });

    test('should return correct tab names', () {
      expect(MainPageNavigation.getTabName(0), 'Dashboard');
      expect(MainPageNavigation.getTabName(1), 'Tracking');
      expect(MainPageNavigation.getTabName(2), 'Matching');
      expect(MainPageNavigation.getTabName(3), 'Search');
      expect(MainPageNavigation.getTabName(4), 'Profile');
    });

    test('should return default tab name for invalid index', () {
      expect(MainPageNavigation.getTabName(-1), 'Dashboard');
      expect(MainPageNavigation.getTabName(5), 'Dashboard');
      expect(MainPageNavigation.getTabName(100), 'Dashboard');
    });
  });
}
