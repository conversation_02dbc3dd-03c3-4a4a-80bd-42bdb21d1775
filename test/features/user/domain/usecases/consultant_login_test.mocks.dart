// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in mcdc/test/features/user/domain/usecases/consultant_login_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:mcdc/core/error/failures.dart' as _i5;
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart'
    as _i15;
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart'
    as _i7;
import 'package:mcdc/features/user/domain/entities/create_password_request.dart'
    as _i14;
import 'package:mcdc/features/user/domain/entities/create_password_response.dart'
    as _i13;
import 'package:mcdc/features/user/domain/entities/forgot_password_response.dart'
    as _i11;
import 'package:mcdc/features/user/domain/entities/forgot_password_verify_otp_response.dart'
    as _i12;
import 'package:mcdc/features/user/domain/entities/login_data.dart' as _i6;
import 'package:mcdc/features/user/domain/entities/member_register_request.dart'
    as _i10;
import 'package:mcdc/features/user/domain/entities/member_register_response.dart'
    as _i9;
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart'
    as _i8;
import 'package:mcdc/features/user/domain/entities/session_info.dart' as _i17;
import 'package:mcdc/features/user/domain/entities/user.dart' as _i16;
import 'package:mcdc/features/user/domain/repositories/user_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i3.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>> memberLogin({
    required String? username,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#memberLogin, [], {
              #username: username,
              #password: password,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>>.value(
                  _FakeEither_0<_i5.Failure, _i6.LoginData>(
                    this,
                    Invocation.method(#memberLogin, [], {
                      #username: username,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>> consultantLogin({
    required String? username,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#consultantLogin, [], {
              #username: username,
              #password: password,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>>.value(
                  _FakeEither_0<_i5.Failure, _i6.LoginData>(
                    this,
                    Invocation.method(#consultantLogin, [], {
                      #username: username,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.LoginData>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.CheckIdCardResult>> memberCheckIdCard({
    required String? idCard,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#memberCheckIdCard, [], {#idCard: idCard}),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i7.CheckIdCardResult>
            >.value(
              _FakeEither_0<_i5.Failure, _i7.CheckIdCardResult>(
                this,
                Invocation.method(#memberCheckIdCard, [], {#idCard: idCard}),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.CheckIdCardResult>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i8.RegisterValidationError>>
  validateMemberRegister({
    required String? username,
    required String? email,
    required String? phone,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateMemberRegister, [], {
              #username: username,
              #email: email,
              #phone: phone,
            }),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i8.RegisterValidationError>
            >.value(
              _FakeEither_0<_i5.Failure, _i8.RegisterValidationError>(
                this,
                Invocation.method(#validateMemberRegister, [], {
                  #username: username,
                  #email: email,
                  #phone: phone,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i8.RegisterValidationError>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.MemberRegisterResponse>>
  memberRegister({required _i10.MemberRegisterRequest? request}) =>
      (super.noSuchMethod(
            Invocation.method(#memberRegister, [], {#request: request}),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i9.MemberRegisterResponse>
            >.value(
              _FakeEither_0<_i5.Failure, _i9.MemberRegisterResponse>(
                this,
                Invocation.method(#memberRegister, [], {#request: request}),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.MemberRegisterResponse>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i11.ForgotPasswordResponse>>
  forgotPasswordRequest({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#forgotPasswordRequest, [], {#email: email}),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i11.ForgotPasswordResponse>
            >.value(
              _FakeEither_0<_i5.Failure, _i11.ForgotPasswordResponse>(
                this,
                Invocation.method(#forgotPasswordRequest, [], {#email: email}),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i11.ForgotPasswordResponse>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i12.ForgotPasswordVerifyOtpResponse>>
  forgotPasswordVerifyOtp({
    required String? email,
    required String? otp,
    required String? refCode,
    required String? otpToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#forgotPasswordVerifyOtp, [], {
              #email: email,
              #otp: otp,
              #refCode: refCode,
              #otpToken: otpToken,
            }),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i12.ForgotPasswordVerifyOtpResponse>
            >.value(
              _FakeEither_0<_i5.Failure, _i12.ForgotPasswordVerifyOtpResponse>(
                this,
                Invocation.method(#forgotPasswordVerifyOtp, [], {
                  #email: email,
                  #otp: otp,
                  #refCode: refCode,
                  #otpToken: otpToken,
                }),
              ),
            ),
          )
          as _i4.Future<
            _i2.Either<_i5.Failure, _i12.ForgotPasswordVerifyOtpResponse>
          >);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i13.CreatePasswordResponse>>
  createPassword({required _i14.CreatePasswordRequest? request}) =>
      (super.noSuchMethod(
            Invocation.method(#createPassword, [], {#request: request}),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i13.CreatePasswordResponse>
            >.value(
              _FakeEither_0<_i5.Failure, _i13.CreatePasswordResponse>(
                this,
                Invocation.method(#createPassword, [], {#request: request}),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i13.CreatePasswordResponse>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> logout({required String? token}) =>
      (super.noSuchMethod(
            Invocation.method(#logout, [], {#token: token}),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#logout, [], {#token: token}),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);
}

/// A class which mocks [AuthStorageDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthStorageDataSource extends _i1.Mock
    implements _i15.AuthStorageDataSource {
  MockAuthStorageDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> saveLoginData(_i6.LoginData? loginData) =>
      (super.noSuchMethod(
            Invocation.method(#saveLoginData, [loginData]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String?> getAccessToken() =>
      (super.noSuchMethod(
            Invocation.method(#getAccessToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<_i16.User?> getUser() =>
      (super.noSuchMethod(
            Invocation.method(#getUser, []),
            returnValue: _i4.Future<_i16.User?>.value(),
          )
          as _i4.Future<_i16.User?>);

  @override
  _i4.Future<_i17.SessionInfo?> getSessionInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionInfo, []),
            returnValue: _i4.Future<_i17.SessionInfo?>.value(),
          )
          as _i4.Future<_i17.SessionInfo?>);

  @override
  _i4.Future<_i6.LoginData?> getLoginData() =>
      (super.noSuchMethod(
            Invocation.method(#getLoginData, []),
            returnValue: _i4.Future<_i6.LoginData?>.value(),
          )
          as _i4.Future<_i6.LoginData?>);

  @override
  _i4.Future<bool> isLoggedIn() =>
      (super.noSuchMethod(
            Invocation.method(#isLoggedIn, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> clearAuthData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAuthData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateAccessToken(String? accessToken) =>
      (super.noSuchMethod(
            Invocation.method(#updateAccessToken, [accessToken]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateRefreshToken(String? refreshToken) =>
      (super.noSuchMethod(
            Invocation.method(#updateRefreshToken, [refreshToken]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
