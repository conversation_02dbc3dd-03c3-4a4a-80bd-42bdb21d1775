import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart';
import 'package:mcdc/features/user/domain/usecases/check_duplicate_id_card.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_state.dart';

import 'check_id_card_cubit_test.mocks.dart';

@GenerateMocks([CheckDuplicateIdCard])
void main() {
  group('CheckIdCardCubit', () {
    late CheckIdCardCubit cubit;
    late MockCheckDuplicateIdCard mockCheckDuplicateIdCard;

    setUp(() {
      mockCheckDuplicateIdCard = MockCheckDuplicateIdCard();
      cubit = CheckIdCardCubit(checkDuplicateIdCard: mockCheckDuplicateIdCard);
    });

    tearDown(() {
      cubit.close();
    });

    test('initial state is idle', () {
      expect(cubit.state, equals(const CheckIdCardState.idle()));
    });

    group('validateAndCheckIdCard', () {
      blocTest<CheckIdCardCubit, CheckIdCardState>(
        'emits invalid state for invalid length and stops validation',
        build: () => cubit,
        act: (cubit) async {
          await cubit.validateAndCheckIdCard('123456789012'); // 12 digits
        },
        expect:
            () => [
              isA<CheckIdCardInvalid>(), // Local validation error only
            ],
        verify: (cubit) {
          // Should remain in invalid state
          expect(cubit.state, isA<CheckIdCardInvalid>());
          expect(cubit.hasError, isTrue);
          // API should not be called for invalid input
          verifyNever(mockCheckDuplicateIdCard(any));
        },
      );

      blocTest<CheckIdCardCubit, CheckIdCardState>(
        'emits invalid state for invalid checksum and stops validation',
        build: () => cubit,
        act: (cubit) async {
          await cubit.validateAndCheckIdCard(
            '1234567890120',
          ); // Invalid checksum
        },
        expect:
            () => [
              isA<CheckIdCardInvalid>(), // Local validation error only
            ],
        verify: (cubit) {
          // Should remain in invalid state
          expect(cubit.state, isA<CheckIdCardInvalid>());
          expect(cubit.hasError, isTrue);
          // API should not be called for invalid input
          verifyNever(mockCheckDuplicateIdCard(any));
        },
      );

      blocTest<CheckIdCardCubit, CheckIdCardState>(
        'emits invalid state when valid ID card exists (duplicate)',
        build: () => cubit,
        setUp: () {
          when(mockCheckDuplicateIdCard(any)).thenAnswer(
            (_) async => const Right(CheckIdCardResult(isDuplicate: true)),
          );
        },
        act: (cubit) async {
          // Use a valid ID card that passes local validation
          await cubit.validateAndCheckIdCard('1234567890121'); // Valid checksum
        },
        expect:
            () => [
              const CheckIdCardState.loading(), // Loading state
              isA<CheckIdCardInvalid>(), // API result (duplicate)
            ],
        verify: (cubit) {
          expect(cubit.getErrorMessage(), contains('ระบบแล้ว'));
          // API should be called for valid input
          verify(mockCheckDuplicateIdCard(any)).called(1);
        },
      );

      blocTest<CheckIdCardCubit, CheckIdCardState>(
        'emits valid state for valid and non-duplicate ID card',
        build: () => cubit,
        setUp: () {
          when(mockCheckDuplicateIdCard(any)).thenAnswer(
            (_) async => const Right(CheckIdCardResult(isDuplicate: false)),
          );
        },
        act: (cubit) async {
          // Use a valid ID card that passes local validation
          await cubit.validateAndCheckIdCard('1234567890121'); // Valid checksum
        },
        expect:
            () => [
              const CheckIdCardState.loading(), // Loading state
              const CheckIdCardState.valid(), // API result (valid)
            ],
        verify: (cubit) {
          expect(cubit.getErrorMessage(), isNull);
          expect(cubit.hasError, isFalse);
          // API should be called for valid input
          verify(mockCheckDuplicateIdCard(any)).called(1);
        },
      );

      blocTest<CheckIdCardCubit, CheckIdCardState>(
        'emits invalid state when API fails for valid ID card',
        build: () => cubit,
        setUp: () {
          when(mockCheckDuplicateIdCard(any)).thenAnswer(
            (_) async => const Left(ServerFailure(message: 'Server error')),
          );
        },
        act: (cubit) async {
          // Use a valid ID card that passes local validation
          await cubit.validateAndCheckIdCard('1234567890121'); // Valid checksum
        },
        expect:
            () => [
              const CheckIdCardState.loading(), // Loading state
              isA<CheckIdCardInvalid>(), // API failure result
            ],
        verify: (cubit) {
          // Note: The cubit emits a generic error message, not the server error message
          expect(cubit.getErrorMessage(), isNotNull);
          expect(cubit.hasError, isTrue);
          // API should be called for valid input
          verify(mockCheckDuplicateIdCard(any)).called(1);
        },
      );
    });

    group('helper methods', () {
      test('hasError returns true for invalid state', () {
        cubit.emit(const CheckIdCardState.invalid('Test error'));
        expect(cubit.hasError, isTrue);
      });

      test('hasError returns false for valid state', () {
        cubit.emit(const CheckIdCardState.valid());
        expect(cubit.hasError, isFalse);
      });

      test('getErrorMessage returns message for invalid state', () {
        const errorMessage = 'Test error message';
        cubit.emit(const CheckIdCardState.invalid(errorMessage));
        expect(cubit.getErrorMessage(), equals(errorMessage));
      });

      test('getErrorMessage returns null for valid state', () {
        cubit.emit(const CheckIdCardState.valid());
        expect(cubit.getErrorMessage(), isNull);
      });
    });

    test('reset sets state to idle', () {
      cubit.emit(const CheckIdCardState.invalid('Test error'));
      cubit.reset();
      expect(cubit.state, equals(const CheckIdCardState.idle()));
    });

    test('clearError sets invalid state to idle', () {
      cubit.emit(const CheckIdCardState.invalid('Test error'));
      cubit.clearError();
      expect(cubit.state, equals(const CheckIdCardState.idle()));
      expect(cubit.hasError, isFalse);
    });

    test('clearError does not change non-invalid states', () {
      cubit.emit(const CheckIdCardState.valid());
      cubit.clearError();
      expect(cubit.state, equals(const CheckIdCardState.valid()));

      cubit.emit(const CheckIdCardState.loading());
      cubit.clearError();
      expect(cubit.state, equals(const CheckIdCardState.loading()));
    });
  });
}
