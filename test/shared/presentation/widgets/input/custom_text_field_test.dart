import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/core/constants/app_colors.dart';

void main() {
  group('CustomTextField', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    Widget createTestWidget({
      String? forceErrorText,
      String? Function(String?)? validator,
      bool isRequired = false,
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder:
            (context, child) => MaterialApp(
              home: Scaffold(
                body: CustomTextField(
                  controller: controller,
                  label: 'Test Label',
                  forceErrorText: forceErrorText,
                  validator: validator,
                  isRequired: isRequired,
                ),
              ),
            ),
      );
    }

    testWidgets('displays label correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      expect(find.text('Test Label'), findsOneWidget);
    });

    testWidgets('displays required asterisk when isRequired is true', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget(isRequired: true));

      // Look for RichText widget that contains the asterisk
      final richTextFinder = find.byType(RichText);
      expect(richTextFinder, findsOneWidget);

      final richText = tester.widget<RichText>(richTextFinder);
      final textSpan = richText.text as TextSpan;
      expect(textSpan.children, isNotNull);
      expect(textSpan.children!.length, greaterThan(0));

      // Check if any child contains the asterisk
      final hasAsterisk = textSpan.children!.any(
        (child) => child is TextSpan && child.text?.contains('*') == true,
      );
      expect(hasAsterisk, isTrue);
    });

    testWidgets('displays forceErrorText when provided', (
      WidgetTester tester,
    ) async {
      const errorText = 'This is a forced error';
      await tester.pumpWidget(createTestWidget(forceErrorText: errorText));

      await tester.pump();
      expect(find.text(errorText), findsOneWidget);
      expect(find.byIcon(Icons.cancel), findsOneWidget);
    });

    testWidgets('displays validation error when validator returns error', (
      WidgetTester tester,
    ) async {
      const errorText = 'Validation error';
      await tester.pumpWidget(
        createTestWidget(
          validator: (value) => value?.isEmpty == true ? errorText : null,
        ),
      );

      // Trigger validation by entering and clearing text
      await tester.enterText(find.byType(TextFormField), 'test');
      await tester.pump();
      await tester.enterText(find.byType(TextFormField), '');
      await tester.pump();

      // Wait for the validation to complete
      await tester.pump(const Duration(milliseconds: 100));

      expect(find.text(errorText), findsOneWidget);
      expect(find.byIcon(Icons.cancel), findsOneWidget);
    });

    testWidgets('clears validation error when user starts typing', (
      WidgetTester tester,
    ) async {
      const errorText = 'Validation error';
      await tester.pumpWidget(
        createTestWidget(
          validator: (value) => value?.isEmpty == true ? errorText : null,
        ),
      );

      // Trigger validation error
      await tester.enterText(find.byType(TextFormField), 'test');
      await tester.pump();
      await tester.enterText(find.byType(TextFormField), '');
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      expect(find.text(errorText), findsOneWidget);

      // Start typing again to clear error
      await tester.enterText(find.byType(TextFormField), 'new text');
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      expect(find.text(errorText), findsNothing);
    });

    testWidgets('prioritizes forceErrorText over validation error', (
      WidgetTester tester,
    ) async {
      const forceError = 'Forced error';
      const validationError = 'Validation error';

      await tester.pumpWidget(
        createTestWidget(
          forceErrorText: forceError,
          validator: (value) => validationError,
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text(forceError), findsOneWidget);
      expect(find.text(validationError), findsNothing);
    });
  });
}
