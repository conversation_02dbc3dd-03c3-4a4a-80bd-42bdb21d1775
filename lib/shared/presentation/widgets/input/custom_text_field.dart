import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class CustomTextField extends StatefulWidget {
  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hintText,
    this.forceErrorText,
    this.validator,
    this.autovalidateMode,
    this.onChanged,
    this.keyboardType = TextInputType.text,
    this.autocorrect = false,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.readOnly = false,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputAction,
    this.onSubmitted,
    this.autofocus = false,
    this.textCapitalization = TextCapitalization.none,
    this.focusNode,
    this.contentPadding,
    this.isRequired = false,
  });

  final TextEditingController controller;
  final String label;
  final String? hintText;
  final String? forceErrorText;
  final String? Function(String?)? validator;
  final AutovalidateMode? autovalidateMode;
  final void Function(String)? onChanged;
  final TextInputType keyboardType;
  final bool autocorrect;
  final bool obscureText;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool readOnly;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputAction? textInputAction;
  final void Function(String)? onSubmitted;
  final bool autofocus;
  final TextCapitalization textCapitalization;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? contentPadding;
  final bool isRequired;

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  String? _validationError;
  bool _hasBeenValidated = false;

  /// Determines if the field is in an error state
  bool get _hasError {
    return widget.forceErrorText != null || _validationError != null;
  }

  /// Gets the current error text to display
  String? get _currentErrorText {
    return widget.forceErrorText ?? _validationError;
  }

  /// Custom validator that captures validation errors
  String? _customValidator(String? value) {
    if (widget.validator != null) {
      final error = widget.validator!(value);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _validationError = error;
            _hasBeenValidated = true;
          });
        }
      });
      // Return null to prevent TextFormField from showing its own error
      // We'll handle error display ourselves
      return null;
    }
    return null;
  }

  /// Handles text changes and clears validation errors
  void _onTextChanged(String value) {
    if (_hasBeenValidated && _validationError != null) {
      setState(() {
        _validationError = null;
      });
    }
    widget.onChanged?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: widget.controller,
          validator: _customValidator,
          autovalidateMode:
              widget.autovalidateMode ?? AutovalidateMode.onUserInteraction,
          onChanged: _onTextChanged,
          keyboardType: widget.keyboardType,
          autocorrect: widget.autocorrect,
          obscureText: widget.obscureText,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          maxLength: widget.maxLength,
          readOnly: widget.readOnly,
          enabled: widget.enabled,
          textInputAction: widget.textInputAction,
          onFieldSubmitted: widget.onSubmitted,
          autofocus: widget.autofocus,
          textCapitalization: widget.textCapitalization,
          focusNode: widget.focusNode,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
          decoration: InputDecoration(
            alignLabelWithHint: widget.maxLines != null && widget.maxLines! > 1,
            label:
                widget.isRequired
                    ? RichText(
                      text: TextSpan(
                        text: widget.label,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color:
                              widget.controller.text.isNotEmpty ||
                                      widget.focusNode?.hasFocus == true
                                  ? AppColors.textSubdude
                                  : AppColors.textDefaultDark,
                          height: 1.5,
                          fontFamily: AppFonts.notoSansThai,
                        ),
                        children: [
                          TextSpan(
                            text: " *",
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.critical,
                              height: 1.5,
                              fontFamily: AppFonts.notoSansThai,
                            ),
                          ),
                        ],
                      ),
                    )
                    : Text(
                      widget.label,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color:
                            widget.controller.text.isNotEmpty ||
                                    widget.focusNode?.hasFocus == true
                                ? AppColors.textSubdude
                                : AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                    ),
            labelStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color:
                  widget.controller.text.isNotEmpty ||
                          widget.focusNode?.hasFocus == true
                      ? AppColors.textSubdude
                      : AppColors.textDefaultDark,
              height: 1.5,
            ),
            hintText: widget.hintText,
            hintStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.hintText,
              height: 1.5,
            ),
            // Hide default error text since we'll show it separately
            errorStyle: const TextStyle(fontSize: 0, height: 0),
            contentPadding:
                widget.contentPadding ??
                EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: _hasError ? AppColors.critical : AppColors.borderDefault,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: _hasError ? AppColors.critical : AppColors.borderDefault,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color:
                    _hasError ? AppColors.critical : AppColors.borderSecondary,
                width: 1,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.critical, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.critical, width: 1),
            ),
            filled: true,
            fillColor:
                _hasError
                    ? AppColors
                        .surfaceCriticalSubdude // Light red background for error state
                    : AppColors.backgroundDefault,
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.suffixIcon,
            counterText: "",
          ),
        ),
        // Custom error text display following Figma design
        if (_currentErrorText != null) ...[
          SizedBox(height: 8.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.cancel_outlined,
                size: 14.sp,
                color: AppColors.critical,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  _currentErrorText!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textCritical,
                    height: 1.5,
                    fontFamily: AppFonts.notoSansThai,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}
