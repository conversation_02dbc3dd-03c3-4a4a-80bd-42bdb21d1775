import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/features/consultant_matching/presentation/matching_home_page.dart';
import 'package:mcdc/features/dashboard/presentation/dashboard_home_page.dart';
import 'package:mcdc/features/user/presentation/profile_main_page.dart';
import 'package:mcdc/features/search/presentation/search_home_page.dart';
import 'package:mcdc/features/tracking/presentation/tracking_home_page.dart';

@RoutePage()
class MainPage extends StatefulWidget {
  /// Optional initial tab index to open the main page with a specific tab selected
  /// Defaults to 0 (Dashboard/Home tab) if not provided
  /// Valid values: 0-4 (Dashboard, Tracking, Matching, Search, Profile)
  final int? initialTabIndex;

  const MainPage({super.key, this.initialTabIndex});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'MainPage',
  );
  late int currentIndex;
  late PageController pageController;
  List<StatefulWidget> contents = [
    const DashboardHomePage(),
    const TrackingHomePage(),
    const MatchingHomePage(),
    const SearchHomePage(),
    const ProfileMainPage(),
  ];

  @override
  void initState() {
    super.initState();
    // Initialize with the provided tab index or default to 0
    // Ensure the index is within valid range (0-4)
    currentIndex = _getValidTabIndex(widget.initialTabIndex);
    pageController = PageController(initialPage: currentIndex);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check for query parameters in the route
    final routeData = context.routeData;
    final tabIndexParam = routeData.queryParams.optString('tabIndex');
    if (tabIndexParam != null) {
      final tabIndex = int.tryParse(tabIndexParam);
      if (tabIndex != null && tabIndex != currentIndex) {
        final validIndex = _getValidTabIndex(tabIndex);
        setState(() {
          currentIndex = validIndex;
          pageController.jumpToPage(validIndex);
        });
      }
    }
  }

  /// Validates and returns a valid tab index
  /// Returns 0 (Dashboard) if the provided index is null or out of range
  int _getValidTabIndex(int? index) {
    if (index == null || index < 0 || index >= contents.length) {
      return 0; // Default to Dashboard tab
    }
    return index;
  }

  /// Navigate to a specific tab programmatically
  /// This method can be called from other parts of the app to switch tabs
  void navigateToTab(int tabIndex) {
    final validIndex = _getValidTabIndex(tabIndex);
    setState(() {
      currentIndex = validIndex;
      pageController.jumpToPage(validIndex);
    });
  }

  void _onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  void _changePage(int index) {
    setState(() {
      pageController.jumpToPage(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: pageController,
        onPageChanged: _onPageChanged,
        children: contents,
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          bottomNavigationBarTheme: BottomNavigationBarThemeData(
            selectedLabelStyle: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              height: kTextHeightNone,
            ),
            unselectedLabelStyle: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              height: kTextHeightNone,
            ),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: currentIndex,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppColors.textPrimary,
          unselectedItemColor: AppColors.textSubdude,
          items: [
            _createBottomNavItem(
              context,
              "assets/icons/nav_home.svg",
              AppLocalizations.of(context)!.navHome,
              currentIndex == 0,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_tracking.svg",
              AppLocalizations.of(context)!.navTracking,
              currentIndex == 1,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_match.svg",
              AppLocalizations.of(context)!.navMatching,
              currentIndex == 2,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_search.svg",
              AppLocalizations.of(context)!.navSearch,
              currentIndex == 3,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_profile.svg",
              AppLocalizations.of(context)!.navProfile,
              currentIndex == 4,
            ),
          ],
          onTap: _changePage,
        ),
      ),
    );
  }

  BottomNavigationBarItem _createBottomNavItem(
    BuildContext context,
    String svgScr,
    String title,
    bool isActive,
  ) {
    return BottomNavigationBarItem(
      icon: Padding(
        padding: const EdgeInsets.only(bottom: 4.0),
        child: SvgPicture.asset(
          svgScr,
          height: 25.h,
          colorFilter: ColorFilter.mode(
            isActive ? AppColors.textPrimary : AppColors.textSubdude,
            BlendMode.srcIn,
          ),
        ),
      ),
      label: title,
      backgroundColor: Colors.transparent,
    );
  }
}
