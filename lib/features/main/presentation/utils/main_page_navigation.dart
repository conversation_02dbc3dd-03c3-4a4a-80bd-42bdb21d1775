import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Utility class for navigating to the main page with specific tab selections
class MainPageNavigation {
  /// Tab indices for the main page
  static const int dashboardTab = 0;
  static const int trackingTab = 1;
  static const int matchingTab = 2;
  static const int searchTab = 3;
  static const int profileTab = 4;

  /// Navigate to the main page with a specific tab selected
  ///
  /// [context] - The build context for navigation
  /// [tabIndex] - The index of the tab to select (0-4)
  /// [replace] - Whether to replace the current route or push a new one
  static Future<void> navigateToTab(
    BuildContext context,
    int tabIndex, {
    bool replace = false,
  }) async {
    // Validate tab index
    if (tabIndex < 0 || tabIndex > 4) {
      tabIndex = 0; // Default to dashboard
    }

    final router = context.router;

    if (replace) {
      await router.replaceAll([MainRoute(initialTabIndex: tabIndex)]);
    } else {
      await router.push(MainRoute(initialTabIndex: tabIndex));
    }
  }

  /// Navigate to the dashboard tab
  static Future<void> navigateToDashboard(
    BuildContext context, {
    bool replace = false,
  }) => navigateToTab(context, dashboardTab, replace: replace);

  /// Navigate to the tracking tab
  static Future<void> navigateToTracking(
    BuildContext context, {
    bool replace = false,
  }) => navigateToTab(context, trackingTab, replace: replace);

  /// Navigate to the matching tab
  static Future<void> navigateToMatching(
    BuildContext context, {
    bool replace = false,
  }) => navigateToTab(context, matchingTab, replace: replace);

  /// Navigate to the search tab
  static Future<void> navigateToSearch(
    BuildContext context, {
    bool replace = false,
  }) => navigateToTab(context, searchTab, replace: replace);

  /// Navigate to the profile tab
  static Future<void> navigateToProfile(
    BuildContext context, {
    bool replace = false,
  }) => navigateToTab(context, profileTab, replace: replace);

  /// Get the tab name for a given index
  static String getTabName(int tabIndex) {
    switch (tabIndex) {
      case dashboardTab:
        return 'Dashboard';
      case trackingTab:
        return 'Tracking';
      case matchingTab:
        return 'Matching';
      case searchTab:
        return 'Search';
      case profileTab:
        return 'Profile';
      default:
        return 'Dashboard';
    }
  }
}
