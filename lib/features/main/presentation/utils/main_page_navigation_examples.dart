import 'package:flutter/material.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

/// Examples of how to use the MainPageNavigation utility
/// This file demonstrates various ways to navigate to specific tabs in the main page
class MainPageNavigationExamples {
  
  /// Example 1: Navigate to profile tab from a button press
  static void navigateToProfileExample(BuildContext context) {
    MainPageNavigation.navigateToProfile(context);
  }

  /// Example 2: Navigate to tracking tab and replace current route
  static void navigateToTrackingWithReplaceExample(BuildContext context) {
    MainPageNavigation.navigateToTracking(context, replace: true);
  }

  /// Example 3: Navigate to a specific tab by index
  static void navigateToSpecificTabExample(BuildContext context, int tabIndex) {
    MainPageNavigation.navigateToTab(context, tabIndex);
  }

  /// Example 4: Navigate to search tab from notification action
  static void navigateToSearchFromNotificationExample(BuildContext context) {
    MainPageNavigation.navigateToSearch(context, replace: true);
  }

  /// Example 5: Navigate to matching tab from deep link
  static void navigateToMatchingFromDeepLinkExample(BuildContext context) {
    MainPageNavigation.navigateToMatching(context);
  }

  /// Example widget that demonstrates navigation buttons
  static Widget buildNavigationExampleWidget(BuildContext context) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () => MainPageNavigation.navigateToDashboard(context),
          child: const Text('Go to Dashboard'),
        ),
        ElevatedButton(
          onPressed: () => MainPageNavigation.navigateToTracking(context),
          child: const Text('Go to Tracking'),
        ),
        ElevatedButton(
          onPressed: () => MainPageNavigation.navigateToMatching(context),
          child: const Text('Go to Matching'),
        ),
        ElevatedButton(
          onPressed: () => MainPageNavigation.navigateToSearch(context),
          child: const Text('Go to Search'),
        ),
        ElevatedButton(
          onPressed: () => MainPageNavigation.navigateToProfile(context),
          child: const Text('Go to Profile'),
        ),
      ],
    );
  }
}

/// Usage examples in different scenarios:
/// 
/// 1. From a notification tap:
/// ```dart
/// void onNotificationTap(BuildContext context) {
///   MainPageNavigation.navigateToProfile(context, replace: true);
/// }
/// ```
/// 
/// 2. From a deep link handler:
/// ```dart
/// void handleDeepLink(BuildContext context, String link) {
///   if (link.contains('/profile')) {
///     MainPageNavigation.navigateToProfile(context);
///   } else if (link.contains('/tracking')) {
///     MainPageNavigation.navigateToTracking(context);
///   }
/// }
/// ```
/// 
/// 3. From a drawer menu item:
/// ```dart
/// ListTile(
///   title: Text('My Profile'),
///   onTap: () {
///     Navigator.pop(context); // Close drawer
///     MainPageNavigation.navigateToProfile(context);
///   },
/// )
/// ```
/// 
/// 4. Using URL with query parameters:
/// ```
/// /main?tabIndex=4  // Opens main page with profile tab selected
/// /main?tabIndex=1  // Opens main page with tracking tab selected
/// /main?tabIndex=0  // Opens main page with dashboard tab selected (default)
/// ```
/// 
/// 5. Programmatic navigation with validation:
/// ```dart
/// void navigateToTabSafely(BuildContext context, int? tabIndex) {
///   // The navigation utility automatically validates the tab index
///   // Invalid indices default to 0 (dashboard)
///   MainPageNavigation.navigateToTab(context, tabIndex ?? 0);
/// }
/// ```
