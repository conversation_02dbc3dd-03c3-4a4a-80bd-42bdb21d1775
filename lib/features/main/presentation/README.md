# Main Page Dynamic Routing

This document explains how to use the dynamic routing functionality for the main page, which allows opening the main page with a specific tab pre-selected.

## Overview

The main page now supports dynamic routing that enables:
- Opening the main page with a specific tab selected
- Deep linking to specific tabs
- Programmatic navigation to specific tabs
- Query parameter support for tab selection

## Features

### 1. Tab Index Parameter Support

The `MainPage` widget now accepts an optional `initialTabIndex` parameter:

```dart
MainPage(initialTabIndex: 2) // Opens with Matching tab selected
```

### 2. Query Parameter Support

The main page can read tab index from URL query parameters:

```
/main?tabIndex=4  // Opens with Profile tab selected
/main?tabIndex=1  // Opens with Tracking tab selected
/main?tabIndex=0  // Opens with Dashboard tab selected (default)
```

### 3. Navigation Utility

A utility class `MainPageNavigation` provides convenient methods for navigation:

```dart
// Navigate to specific tabs
MainPageNavigation.navigateToProfile(context);
MainPageNavigation.navigateToTracking(context);
MainPageNavigation.navigateToMatching(context);
MainPageNavigation.navigateToSearch(context);
MainPageNavigation.navigateToDashboard(context);

// Navigate with replace option
MainPageNavigation.navigateToProfile(context, replace: true);

// Navigate to specific tab by index
MainPageNavigation.navigateToTab(context, 2); // Matching tab
```

## Tab Indices

The following tab indices are available:

| Tab | Index | Constant |
|-----|-------|----------|
| Dashboard | 0 | `MainPageNavigation.dashboardTab` |
| Tracking | 1 | `MainPageNavigation.trackingTab` |
| Matching | 2 | `MainPageNavigation.matchingTab` |
| Search | 3 | `MainPageNavigation.searchTab` |
| Profile | 4 | `MainPageNavigation.profileTab` |

## Usage Examples

### 1. From Notification Tap

```dart
void onNotificationTap(BuildContext context) {
  MainPageNavigation.navigateToProfile(context, replace: true);
}
```

### 2. From Deep Link Handler

```dart
void handleDeepLink(BuildContext context, String link) {
  if (link.contains('/profile')) {
    MainPageNavigation.navigateToProfile(context);
  } else if (link.contains('/tracking')) {
    MainPageNavigation.navigateToTracking(context);
  }
}
```

### 3. From Drawer Menu

```dart
ListTile(
  title: Text('My Profile'),
  onTap: () {
    Navigator.pop(context); // Close drawer
    MainPageNavigation.navigateToProfile(context);
  },
)
```

### 4. Direct Route Usage

```dart
// Using auto_route directly
context.router.push(MainRoute(initialTabIndex: 3)); // Search tab

// Replace current route
context.router.replaceAll([MainRoute(initialTabIndex: 4)]); // Profile tab
```

## Implementation Details

### MainPage Changes

1. Added `initialTabIndex` parameter to constructor
2. Added validation for tab index range (0-4)
3. Added query parameter reading in `didChangeDependencies`
4. Added `navigateToTab` method for programmatic navigation

### Auto Route Integration

The auto_route generator automatically creates:
- `MainRoute` with `initialTabIndex` parameter
- `MainRouteArgs` class for type-safe parameter passing
- Proper route building with parameter validation

### Validation

- Invalid tab indices automatically default to 0 (Dashboard)
- Null values are handled gracefully
- Out-of-range indices are clamped to valid range

## Error Handling

The implementation includes robust error handling:
- Invalid tab indices default to Dashboard (index 0)
- Null parameters are handled gracefully
- Query parameter parsing errors are ignored

## Performance Considerations

- Tab switching uses `PageController.jumpToPage()` for instant navigation
- Query parameter reading only occurs when route dependencies change
- Validation is performed once during initialization

## Testing

To test the functionality:

1. **Direct Navigation**: Use the utility methods in your code
2. **URL Testing**: Navigate to `/main?tabIndex=N` where N is 0-4
3. **Deep Linking**: Test with deep link handlers
4. **Parameter Validation**: Test with invalid indices (should default to 0)

## Migration Guide

Existing code using `MainRoute()` will continue to work without changes. To use the new functionality:

1. Replace `MainRoute()` with `MainRoute(initialTabIndex: index)`
2. Use `MainPageNavigation` utility methods for convenience
3. Update deep link handlers to use the new navigation methods

## Future Enhancements

Potential future improvements:
- Named tab routing (e.g., `/main/profile`)
- Tab state preservation across navigation
- Animation customization for tab switching
- Integration with browser history for web support
