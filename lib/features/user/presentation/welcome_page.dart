import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/core/enums/user_type.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/features/user/presentation/bloc/auth_status_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/auth_status_state.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';

@RoutePage()
class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'WelcomePage',
  );
  late AuthStatusCubit _authStatusCubit;

  @override
  void initState() {
    super.initState();
    _authStatusCubit = AppInjector.get<AuthStatusCubit>();
    // Check authentication status when page loads
    _authStatusCubit.checkAuthStatus();
  }

  @override
  void dispose() {
    _authStatusCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocConsumer<AuthStatusCubit, AuthStatusState>(
      bloc: _authStatusCubit,
      listener: (context, state) {
        switch (state) {
          case Authenticated(:final loginData):
            // Navigate to main page if user is logged in
            context.router.replaceAll([MainRoute()]);
            break;
          case Error(:final message):
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error checking login status: $message'),
                backgroundColor: Colors.red,
              ),
            );
            break;
          default:
            // For other states, do nothing in listener
            break;
        }
      },
      builder: (context, state) {
        // Show white screen when checking auth status or initial state
        if (state is Initial || state is Checking || state is Authenticated) {
          return const Scaffold(
            backgroundColor: Colors.white,
            body: SizedBox.expand(),
          );
        }

        // Show welcome page content for unauthenticated state or error state
        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Spacer(),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      l10n.welcome,
                      style: TextStyle(
                        color: AppColors.textDefaultDark,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        height: 1.5,
                      ),
                    ),
                  ),
                  8.h.verticalSpace,
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      l10n.appDescription,
                      style: TextStyle(
                        color: AppColors.textDefaultDark,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: SvgPicture.asset(
                      'assets/images/online_world_cuate.svg',
                      height: 347.h,
                    ),
                  ),
                  const Spacer(),
                  // Primary button - For members
                  PrimaryButton(
                    text: l10n.forMembers,
                    width: double.infinity,
                    height: 52.h,
                    borderRadius: 50.r,
                    onPressed: () {
                      context.router.push(
                        LoginRoute(userType: UserType.member),
                      );
                    },
                  ),
                  24.h.verticalSpace,
                  // Secondary button - For consultants
                  SecondaryButton(
                    text: l10n.forConsultants,
                    width: double.infinity,
                    height: 52.h,
                    borderRadius: 50.r,
                    onPressed: () {
                      context.router.push(
                        LoginRoute(userType: UserType.consultant),
                      );
                    },
                  ),
                  24.h.verticalSpace,
                  GestureDetector(
                    onTap: () {
                      context.router.replaceAll([MainRoute()]);
                    },
                    child: Text(
                      l10n.skip,
                      style: TextStyle(
                        color: AppColors.textSubdude,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                    ),
                  ),
                  // Bottom spacer
                  const Spacer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
