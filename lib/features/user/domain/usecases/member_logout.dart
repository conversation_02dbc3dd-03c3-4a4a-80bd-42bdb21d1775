import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Use case for logging out a member user
/// Handles both API token revocation and local storage cleanup
class MemberLogout extends UseCase<void, NoParams> {
  final AuthStorageDataSource _authStorage;
  final UserRepository _userRepository;

  MemberLogout(this._authStorage, this._userRepository);

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    try {
      // Get the access token for API logout call
      final accessToken = await _authStorage.getAccessToken();

      // If we have a token, try to revoke it via API
      if (accessToken != null) {
        final logoutResult = await _userRepository.logout(token: accessToken);

        // If API logout fails, we still proceed with local cleanup
        // but log the failure for debugging purposes
        if (logoutResult.isLeft()) {
          // API logout failed, but we continue with local cleanup
          // In a production app, you might want to log this failure
        }
      }

      // Always clear local authentication data regardless of API result
      await _authStorage.clearAuthData();
      return const Right(null);
    } catch (e) {
      return Left(
        UnhandledFailure(message: 'Failed to logout: ${e.toString()}'),
      );
    }
  }
}
