import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/entities/member_register_response.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Parameters for member registration use case
class MemberRegisterParams extends Equatable {
  /// The member registration request containing all required data
  final MemberRegisterRequest request;

  const MemberRegisterParams({required this.request});

  @override
  List<Object> get props => [request];
}

/// Use case for registering a new member
///
/// This use case handles the complete member registration flow including:
/// - Validating and processing registration data
/// - Calling the repository to register the member
/// - Handling success and error responses
///
/// The use case follows clean architecture principles by:
/// - Depending only on domain layer abstractions
/// - Containing business logic without data layer concerns
/// - Returning `Either<Failure, Success>` for proper error handling
class MemberRegister
    extends UseCase<MemberRegisterResponse, MemberRegisterParams> {
  final UserRepository _userRepository;
  final AuthStorageDataSource _authStorage;

  /// Creates a new instance of [MemberRegister]
  ///
  /// [_userRepository] - Repository for user-related operations
  /// [_authStorage] - Data source for authentication storage operations
  MemberRegister(this._userRepository, this._authStorage);

  @override
  Future<Either<Failure, MemberRegisterResponse>> call(
    MemberRegisterParams params,
  ) async {
    try {
      // Process the registration request with proper null handling for optional fields
      final processedRequest = _processRegistrationRequest(params.request);

      // Call repository to register the member
      final result = await _userRepository.memberRegister(
        request: processedRequest,
      );

      // Handle the result and save login data if registration is successful
      return result.fold((failure) => Left(failure), (response) async {
        if (response.isSuccess && response.loginData != null) {
          try {
            // Save login data to secure storage for automatic login
            await _authStorage.saveLoginData(response.loginData!);
            return Right(response);
          } catch (e) {
            // If saving fails, still return success but the user might need to login manually
            // The registration was successful, but data persistence failed
            return Right(response);
          }
        } else {
          return Right(response);
        }
      });
    } catch (e) {
      // Handle any unexpected exceptions and convert to domain failure
      return Left(
        UnhandledFailure(message: 'Failed to register member: ${e.toString()}'),
      );
    }
  }

  /// Processes the registration request to handle optional field transformations
  ///
  /// Converts zero values to null for optional government sector, ministry,
  /// and department IDs as required by the API
  MemberRegisterRequest _processRegistrationRequest(
    MemberRegisterRequest request,
  ) {
    return MemberRegisterRequest(
      identityCardNo: request.identityCardNo,
      username: request.username,
      password: request.password,
      confirmPassword: request.confirmPassword,
      email: request.email,
      firstName: request.firstName,
      lastName: request.lastName,
      phone: request.phone,
      appMasMemberTypeId: request.appMasMemberTypeId,
      // Convert 0 to null for optional government sector ID
      appMasGovernmentSectorId:
          request.appMasGovernmentSectorId == 0
              ? null
              : request.appMasGovernmentSectorId,
      appMasGovernmentSectorOther: request.appMasGovernmentSectorOther,
      // Convert 0 to null for optional ministry ID
      appMasMinistryId:
          request.appMasMinistryId == 0 ? null : request.appMasMinistryId,
      appMasMinistryOther: request.appMasMinistryOther,
      // Convert 0 to null for optional department ID
      appMasDepartmentId:
          request.appMasDepartmentId == 0 ? null : request.appMasDepartmentId,
      appMasDepartmentOther: request.appMasDepartmentOther,
      name: request.name,
      isNotification: request.isNotification,
      lang: request.lang,
      status: request.status,
      otpToken: request.otpToken,
    );
  }
}
