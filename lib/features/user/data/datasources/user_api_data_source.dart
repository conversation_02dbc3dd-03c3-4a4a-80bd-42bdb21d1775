import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/error/exceptions.dart';
import 'package:mcdc/features/user/data/models/login_data_model.dart';
import 'package:mcdc/features/user/data/models/check_id_card_result_model.dart';
import 'package:mcdc/features/user/data/models/member_register_request_model.dart';
import 'package:mcdc/features/user/data/models/member_register_response_model.dart';
import 'package:mcdc/features/user/data/models/register_validation_error_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_response_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_verify_otp_response_model.dart';
import 'package:mcdc/features/user/data/models/create_password_request_model.dart';
import 'package:mcdc/features/user/data/models/create_password_response_model.dart';

abstract class UserApiDataSource {
  Future<Result<LoginDataModel>> memberLogin({
    required String username,
    required String password,
  });

  Future<Result<LoginDataModel>> consultantLogin({
    required String username,
    required String password,
  });

  Future<Result<CheckIdCardResultModel>> memberCheckIdCard({
    required String idCard,
  });

  Future<Result<RegisterValidationErrorModel>> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  });

  Future<Result<MemberRegisterResponseModel>> memberRegister({
    required MemberRegisterRequestModel request,
  });

  Future<Result<ForgotPasswordResponseModel>> forgotPasswordRequest({
    required String email,
  });

  Future<Result<ForgotPasswordVerifyOtpResponseModel>> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  });

  Future<Result<CreatePasswordResponseModel>> createPassword({
    required CreatePasswordRequestModel request,
  });

  Future<Result<void>> logout({required String token});
}

class UserApiDataSourceImpl implements UserApiDataSource {
  final ApiClient _apiClient;

  const UserApiDataSourceImpl(this._apiClient);

  @override
  Future<Result<LoginDataModel>> memberLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberLogin,
      data: {'username': username, 'password': password},
    );

    // Use the new Result-based approach with flexible parsing
    return result.map<LoginDataModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Try to parse using the flexible response parser
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          LoginDataModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<LoginDataModel>> consultantLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.consultantLogin,
      data: {'username': username, 'password': password},
    );

    return result.map<LoginDataModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          LoginDataModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<CheckIdCardResultModel>> memberCheckIdCard({
    required String idCard,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberCheckIdCard,
      data: {'pid': idCard},
    );

    // Use the new Result-based approach with flexible parsing
    return result.map<CheckIdCardResultModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Try to parse using the flexible response parser
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          CheckIdCardResultModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else if (parseResult.isError && parseResult.data != null) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<RegisterValidationErrorModel>> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.validateMemberRegister,
      data: {'username': username, 'email': email, 'phone': phone},
    );

    // Use ApiResponseParser.parseCustomResponse for validation error structure
    return result.map<RegisterValidationErrorModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Parse the validation error response with nested structure using custom parser
        final parseResult = ApiResponseParser.parseCustomResponse<
          RegisterValidationErrorModel
        >(responseData, (json) {
          final jsonData = json as Map<String, dynamic>;
          final status = jsonData['status'] as bool? ?? false;
          final errorMessage = jsonData['error_message'] as String?;
          final errorCode = jsonData['error_code'];
          final data = jsonData['data'] as Map<String, dynamic>?;

          // Handle both success and error cases
          if (status) {
            // Success case - validation passed, return empty validation errors
            return Result.success(const RegisterValidationErrorModel());
          } else {
            // Error case - extract validation errors from nested structure
            if (data != null && data.containsKey('validation_errors')) {
              final validationErrors =
                  data['validation_errors'] as Map<String, dynamic>?;

              if (validationErrors != null) {
                // Transform the nested validation_errors to match our model structure
                final transformedData = <String, dynamic>{
                  'username': validationErrors['username'],
                  'email': validationErrors['email'],
                  'phone': validationErrors['phone'],
                };

                final model = RegisterValidationErrorModel.fromJson(
                  transformedData,
                );
                return Result.error(
                  message: errorMessage ?? 'Validation failed',
                  code: errorCode?.toString(),
                  data: model,
                );
              }
            }

            // If no validation errors found, return error result
            return Result.error(
              message: errorMessage ?? 'Validation failed',
              code: errorCode?.toString(),
            );
          }
        });

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else if (parseResult.isError && parseResult.data != null) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<MemberRegisterResponseModel>> memberRegister({
    required MemberRegisterRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberRegister,
      data: request.toJson(),
    );

    // Use the new Result-based approach with custom parsing for member registration
    return result.map<MemberRegisterResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        try {
          // Parse the member registration response using the custom factory
          final model = MemberRegisterResponseModel.fromApiResponse(
            responseData,
          );
          return model;
        } catch (e) {
          throw ServerException(
            message:
                'Failed to parse member registration response: ${e.toString()}',
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<ForgotPasswordResponseModel>> forgotPasswordRequest({
    required String email,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetRequest,
      data: {'email': email},
    );

    return result.map<ForgotPasswordResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          ForgotPasswordResponseModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<ForgotPasswordVerifyOtpResponseModel>> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetVerifyOtp,
      data: {
        'email': email,
        'otp': otp,
        'ref_code': refCode,
        'otp_token': otpToken,
      },
    );

    return result.map<ForgotPasswordVerifyOtpResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          ForgotPasswordVerifyOtpResponseModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<CreatePasswordResponseModel>> createPassword({
    required CreatePasswordRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetUpdatePassword,
      data: request.toJson(),
    );

    return result.map<CreatePasswordResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          CreatePasswordResponseModel.fromJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<void>> logout({required String token}) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.logout,
      data: {'token': token},
    );

    return result.map<void>((responseData) {
      // For logout, we don't need to parse any response data
      // Just return void if the request was successful
      return;
    });
  }
}
