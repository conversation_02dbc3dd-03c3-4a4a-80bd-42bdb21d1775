import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';

/// Example demonstrating the enhanced CustomTextField with error state handling
class CustomTextFieldExample extends StatefulWidget {
  const CustomTextFieldExample({super.key});

  @override
  State<CustomTextFieldExample> createState() => _CustomTextFieldExampleState();
}

class _CustomTextFieldExampleState extends State<CustomTextFieldExample> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameController = TextEditingController();
  
  String? _forceErrorText;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Username is required';
    }
    if (value.length < 3) {
      return 'Username must be at least 3 characters';
    }
    return null;
  }

  void _simulateApiError() {
    setState(() {
      _forceErrorText = 'This username is already taken';
    });
  }

  void _clearApiError() {
    setState(() {
      _forceErrorText = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (context, child) => MaterialApp(
        title: 'CustomTextField Example',
        home: Scaffold(
          appBar: AppBar(
            title: const Text('CustomTextField Error States'),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enhanced CustomTextField Examples',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  
                  // Email field with validation
                  CustomTextField(
                    controller: _emailController,
                    label: 'Email Address',
                    hintText: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    validator: _validateEmail,
                    isRequired: true,
                  ),
                  SizedBox(height: 24.h),
                  
                  // Password field with validation
                  CustomTextField(
                    controller: _passwordController,
                    label: 'Password',
                    hintText: 'Enter your password',
                    obscureText: true,
                    validator: _validatePassword,
                    isRequired: true,
                  ),
                  SizedBox(height: 24.h),
                  
                  // Username field with force error (simulating API error)
                  CustomTextField(
                    controller: _usernameController,
                    label: 'Username',
                    hintText: 'Enter your username',
                    validator: _validateUsername,
                    forceErrorText: _forceErrorText,
                    isRequired: true,
                    onChanged: (value) {
                      // Clear API error when user starts typing
                      if (_forceErrorText != null) {
                        _clearApiError();
                      }
                    },
                  ),
                  SizedBox(height: 32.h),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Form is valid!'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          },
                          child: const Text('Validate Form'),
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _simulateApiError,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Simulate API Error'),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  
                  ElevatedButton(
                    onPressed: _clearApiError,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Clear API Error'),
                  ),
                  
                  SizedBox(height: 32.h),
                  
                  // Instructions
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: Colors.blue.withOpacity(0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Features Demonstrated:',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          '• Red border and background when errors occur\n'
                          '• Error icon and text below the field\n'
                          '• Support for both validation and API errors\n'
                          '• forceErrorText takes priority over validation\n'
                          '• Errors clear when user starts typing\n'
                          '• Required field indicator (*)\n'
                          '• Consistent with Figma design specifications',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(const CustomTextFieldExample());
}
